<template>
  <v-app>
    <v-main>
      <v-container fluid class="home-container">
        <!-- Header -->
        <v-app-bar flat class="home-header" height="80">
          <v-container class="d-flex align-center">
            <!-- Logo -->
            <div class="logo-section">
              <div class="openai-logo">
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M22.2819 9.8211a5.9847 5.9847 0 0 0-.5157-4.9108 6.0462 6.0462 0 0 0-6.5098-2.9A6.0651 6.0651 0 0 0 4.9807 4.1818a5.9847 5.9847 0 0 0-3.9977 2.9 6.0462 6.0462 0 0 0 .7427 7.0966 5.98 5.98 0 0 0 .511 4.9107 6.051 6.051 0 0 0 6.5146 2.9001A5.9847 5.9847 0 0 0 13.2599 24a6.0557 6.0557 0 0 0 5.7718-4.2058 5.9894 5.9894 0 0 0 3.9977-2.9001 6.0557 6.0557 0 0 0-.7475-7.0729zm-9.022 12.6081a4.4755 4.4755 0 0 1-2.8764-1.0408l.1419-.0804 4.7783-2.7582a.7948.7948 0 0 0 .3927-.6813v-6.7369l2.02 1.1686a.071.071 0 0 1 .038.052v5.5826a4.504 4.504 0 0 1-4.4945 4.4944zm-9.6607-4.1254a4.4708 4.4708 0 0 1-.5346-3.0137l.142.0852 4.783 2.7582a.7712.7712 0 0 0 .7806 0l5.8428-3.3685v2.3324a.0804.0804 0 0 1-.0332.0615L9.74 19.9502a4.4992 4.4992 0 0 1-6.1408-1.6464zM2.3408 7.8956a4.485 4.485 0 0 1 2.3655-1.9728V11.6a.7664.7664 0 0 0 .3879.6765l5.8144 3.3543-2.0201 1.1685a.0757.0757 0 0 1-.071 0l-4.8303-2.7865A4.504 4.504 0 0 1 2.3408 7.872zm16.5963 3.8558L13.1038 8.364 15.1192 7.2a.0757.0757 0 0 1 .071 0l4.8303 2.7913a4.4944 4.4944 0 0 1-.6765 8.1042v-5.6772a.79.79 0 0 0-.407-.667zm2.0107-3.0231l-.142-.0852-4.7735-2.7818a.7759.7759 0 0 0-.7854 0L9.409 9.2297V6.8974a.0662.0662 0 0 1 .0284-.0615l4.8303-2.7866a4.4992 4.4992 0 0 1 6.6802 4.66zM8.3065 12.863l-2.02-1.1638a.0804.0804 0 0 1-.038-.0567V6.0742a4.4992 4.4992 0 0 1 7.3757-3.4537l-.142.0805L8.704 5.459a.7948.7948 0 0 0-.3927.6813zm1.0976-2.3654l2.602-1.4998 2.6069 1.4998v2.9994l-2.5974 1.4997-2.6067-1.4997Z" fill="#000"/>
                </svg>
              </div>
              <span class="logo-text">ChatGPT</span>
            </div>

            <v-spacer></v-spacer>

            <!-- Navigation -->
            <div class="nav-buttons">
              <v-btn
                variant="text"
                class="nav-btn"
                @click="goToLogin"
              >
                Log in
              </v-btn>
              <v-btn
                color="#10a37f"
                class="signup-btn"
                @click="goToSignup"
              >
                Sign up for free
              </v-btn>
            </div>
          </v-container>
        </v-app-bar>

        <!-- Hero Section -->
        <div class="hero-section">
          <v-container>
            <v-row justify="center">
              <v-col cols="12" md="8" lg="6" class="text-center">
                <h1 class="hero-title">
                  Get answers. Find inspiration. Be more productive.
                </h1>
                <p class="hero-subtitle">
                  Free to use. Easy to try. Just ask and ChatGPT can help with writing, learning, brainstorming, and more.
                </p>
                
                <!-- Demo Chat Interface -->
                <div class="demo-chat">
                  <div class="demo-input">
                    <v-text-field
                      v-model="demoMessage"
                      placeholder="Ask anything"
                      variant="outlined"
                      hide-details
                      class="demo-message-input"
                      readonly
                    >
                      <template v-slot:prepend-inner>
                        <v-btn
                          icon
                          size="small"
                          variant="text"
                        >
                          <v-icon size="20">mdi-paperclip</v-icon>
                        </v-btn>
                      </template>
                      <template v-slot:append-inner>
                        <v-btn
                          icon
                          size="small"
                          variant="text"
                          class="demo-send-btn"
                        >
                          <v-icon size="20">mdi-send</v-icon>
                        </v-btn>
                      </template>
                    </v-text-field>
                  </div>

                  <!-- Quick Actions -->
                  <div class="demo-actions">
                    <v-btn
                      class="demo-action-btn"
                      variant="outlined"
                      size="small"
                    >
                      <v-icon start size="16">mdi-paperclip</v-icon>
                      Attach
                    </v-btn>
                    <v-btn
                      class="demo-action-btn"
                      variant="outlined"
                      size="small"
                    >
                      <v-icon start size="16">mdi-magnify</v-icon>
                      Search
                    </v-btn>
                    <v-btn
                      class="demo-action-btn"
                      variant="outlined"
                      size="small"
                    >
                      <v-icon start size="16">mdi-brain</v-icon>
                      Reason
                    </v-btn>
                    <v-btn
                      class="demo-action-btn"
                      variant="outlined"
                      size="small"
                    >
                      <v-icon start size="16">mdi-microphone</v-icon>
                      Voice
                    </v-btn>
                  </div>
                </div>

                <!-- CTA Buttons -->
                <div class="cta-buttons">
                  <v-btn
                    color="#10a37f"
                    size="large"
                    class="cta-btn primary"
                    @click="startChatting"
                  >
                    Start now
                  </v-btn>
                  <v-btn
                    variant="outlined"
                    size="large"
                    class="cta-btn secondary"
                    @click="downloadApp"
                  >
                    Download the app
                  </v-btn>
                </div>
              </v-col>
            </v-row>
          </v-container>
        </div>

        <!-- Features Section -->
        <div class="features-section">
          <v-container>
            <v-row>
              <v-col cols="12" md="4">
                <div class="feature-card">
                  <div class="feature-icon">
                    <v-icon size="32" color="#10a37f">mdi-lightbulb-outline</v-icon>
                  </div>
                  <h3 class="feature-title">Examples</h3>
                  <div class="feature-examples">
                    <div class="example-item">"Explain quantum computing in simple terms" →</div>
                    <div class="example-item">"Got any creative ideas for a 10 year old's birthday?" →</div>
                    <div class="example-item">"How do I make an HTTP request in Javascript?" →</div>
                  </div>
                </div>
              </v-col>
              <v-col cols="12" md="4">
                <div class="feature-card">
                  <div class="feature-icon">
                    <v-icon size="32" color="#10a37f">mdi-flash</v-icon>
                  </div>
                  <h3 class="feature-title">Capabilities</h3>
                  <div class="feature-examples">
                    <div class="example-item">Remembers what user said earlier in the conversation</div>
                    <div class="example-item">Allows user to provide follow-up corrections</div>
                    <div class="example-item">Trained to decline inappropriate requests</div>
                  </div>
                </div>
              </v-col>
              <v-col cols="12" md="4">
                <div class="feature-card">
                  <div class="feature-icon">
                    <v-icon size="32" color="#10a37f">mdi-alert-triangle-outline</v-icon>
                  </div>
                  <h3 class="feature-title">Limitations</h3>
                  <div class="feature-examples">
                    <div class="example-item">May occasionally generate incorrect information</div>
                    <div class="example-item">May occasionally produce harmful instructions or biased content</div>
                    <div class="example-item">Limited knowledge of world and events after 2021</div>
                  </div>
                </div>
              </v-col>
            </v-row>
          </v-container>
        </div>

        <!-- Footer -->
        <div class="footer-section">
          <v-container>
            <div class="footer-content">
              <p class="footer-text">
                By messaging ChatGPT, you agree to our 
                <a href="#" class="footer-link">Terms</a> and have read our 
                <a href="#" class="footer-link">Privacy Policy</a>.
              </p>
            </div>
          </v-container>
        </div>
      </v-container>
    </v-main>
  </v-app>
</template>

<script>
export default {
  name: 'HomeView',
  data() {
    return {
      demoMessage: 'What can I help with?'
    }
  },
  methods: {
    goToLogin() {
      this.$router.push('/login')
    },
    goToSignup() {
      this.$router.push('/signup')
    },
    startChatting() {
      this.$router.push('/chat')
    },
    downloadApp() {
      console.log('Download app')
    }
  }
}
</script>

<style scoped>
.home-container {
  padding: 0;
  min-height: 100vh;
  background: linear-gradient(135deg, #f7f7f8 0%, #ffffff 100%);
}

.home-header {
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.openai-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: #f7f7f8;
  border-radius: 8px;
}

.logo-text {
  font-size: 24px;
  font-weight: 600;
  color: #2d3748;
}

.nav-buttons {
  display: flex;
  align-items: center;
  gap: 16px;
}

.nav-btn {
  color: #374151;
  text-transform: none;
  font-weight: 500;
}

.signup-btn {
  background-color: #10a37f !important;
  color: white !important;
  text-transform: none;
  font-weight: 500;
}

.hero-section {
  padding: 80px 0 60px;
  text-align: center;
}

.hero-title {
  font-size: 48px;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 24px;
  line-height: 1.2;
}

.hero-subtitle {
  font-size: 20px;
  color: #6b7280;
  margin-bottom: 48px;
  line-height: 1.6;
}

.demo-chat {
  max-width: 600px;
  margin: 0 auto 48px;
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.demo-input {
  margin-bottom: 16px;
}

.demo-message-input {
  background: #f9fafb;
  border-radius: 24px;
}

.demo-message-input :deep(.v-field__outline) {
  border-radius: 24px;
  border-color: #e5e7eb;
}

.demo-send-btn {
  color: #10a37f;
}

.demo-actions {
  display: flex;
  gap: 8px;
  justify-content: center;
  flex-wrap: wrap;
}

.demo-action-btn {
  border: 1px solid #e5e7eb;
  color: #374151;
  text-transform: none;
  font-weight: 500;
}

.cta-buttons {
  display: flex;
  gap: 16px;
  justify-content: center;
  flex-wrap: wrap;
}

.cta-btn {
  text-transform: none;
  font-weight: 600;
  min-width: 160px;
  height: 48px;
}

.cta-btn.primary {
  background-color: #10a37f !important;
  color: white !important;
}

.cta-btn.secondary {
  border: 2px solid #10a37f;
  color: #10a37f;
}

.features-section {
  padding: 60px 0;
  background: white;
}

.feature-card {
  text-align: center;
  padding: 32px 24px;
  height: 100%;
}

.feature-icon {
  margin-bottom: 24px;
}

.feature-title {
  font-size: 24px;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 24px;
}

.feature-examples {
  text-align: left;
}

.example-item {
  padding: 12px 16px;
  margin-bottom: 8px;
  background: #f9fafb;
  border-radius: 8px;
  color: #374151;
  font-size: 14px;
  line-height: 1.5;
}

.footer-section {
  padding: 40px 0;
  background: #f9fafb;
  border-top: 1px solid #e5e7eb;
}

.footer-content {
  text-align: center;
}

.footer-text {
  color: #6b7280;
  font-size: 14px;
  margin: 0;
}

.footer-link {
  color: #10a37f;
  text-decoration: none;
}

.footer-link:hover {
  text-decoration: underline;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .hero-title {
    font-size: 36px;
  }
  
  .hero-subtitle {
    font-size: 18px;
  }
  
  .demo-chat {
    margin: 0 16px 32px;
    padding: 16px;
  }
  
  .demo-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .demo-action-btn {
    width: 200px;
  }
  
  .cta-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .nav-buttons {
    flex-direction: column;
    gap: 8px;
  }
}
</style>

