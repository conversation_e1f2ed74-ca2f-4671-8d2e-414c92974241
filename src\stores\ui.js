import { defineStore } from 'pinia'

export const useUIStore = defineStore('ui', {
  state: () => ({
    sidebarOpen: true,
    sidebarRail: false,
    theme: 'light',
    isMobile: false,
    notifications: [],
    loading: {
      global: false,
      chat: false,
      auth: false
    }
  }),
  
  getters: {
    isDarkMode: (state) => state.theme === 'dark',
    hasNotifications: (state) => state.notifications.length > 0,
    isLoading: (state) => (type) => state.loading[type] || false
  },
  
  actions: {
    toggleSidebar() {
      this.sidebarOpen = !this.sidebarOpen
    },
    
    setSidebarOpen(open) {
      this.sidebarOpen = open
    },
    
    toggleSidebarRail() {
      this.sidebarRail = !this.sidebarRail
    },
    
    setSidebarRail(rail) {
      this.sidebarRail = rail
    },
    
    setTheme(theme) {
      this.theme = theme
      localStorage.setItem('theme', theme)
    },
    
    toggleTheme() {
      this.setTheme(this.theme === 'light' ? 'dark' : 'light')
    },
    
    setMobile(isMobile) {
      this.isMobile = isMobile
      if (isMobile) {
        this.sidebarOpen = false
      }
    },
    
    addNotification(notification) {
      const id = Date.now()
      this.notifications.push({
        id,
        type: 'info',
        duration: 5000,
        ...notification
      })
      
      // Auto remove notification
      if (notification.duration !== 0) {
        setTimeout(() => {
          this.removeNotification(id)
        }, notification.duration || 5000)
      }
      
      return id
    },
    
    removeNotification(id) {
      const index = this.notifications.findIndex(n => n.id === id)
      if (index !== -1) {
        this.notifications.splice(index, 1)
      }
    },
    
    clearNotifications() {
      this.notifications = []
    },
    
    setLoading(type, loading) {
      this.loading[type] = loading
    },
    
    initializeTheme() {
      const savedTheme = localStorage.getItem('theme')
      if (savedTheme) {
        this.theme = savedTheme
      } else {
        // Detect system preference
        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
        this.theme = prefersDark ? 'dark' : 'light'
      }
    },
    
    handleResize() {
      const isMobile = window.innerWidth < 768
      this.setMobile(isMobile)
    }
  }
})

