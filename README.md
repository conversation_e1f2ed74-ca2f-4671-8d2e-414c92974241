# ChatGPT Clone - Vue 3 + Vuetify

这是一个使用 Vue 3 + JavaScript 和 Vuetify 构建的 ChatGPT 官网界面复刻项目。

## 项目特性

- ✅ Vue 3 + JavaScript
- ✅ Vuetify 3 UI 框架
- ✅ Vue Router 4 路由管理
- ✅ Pinia 状态管理
- ✅ Axios 请求封装
- ✅ 响应式设计
- ✅ 复刻 ChatGPT 官网界面设计

## 项目结构

```
chatgpt-clone/
├── src/
│   ├── api/                 # API 接口
│   │   └── index.js
│   ├── components/          # 公共组件
│   ├── plugins/             # 插件配置
│   │   └── vuetify.js
│   ├── router/              # 路由配置
│   │   └── index.js
│   ├── stores/              # Pinia 状态管理
│   │   ├── user.js
│   │   ├── chat.js
│   │   └── ui.js
│   ├── utils/               # 工具函数
│   │   ├── request.js
│   │   └── helpers.js
│   ├── views/               # 页面组件
│   │   ├── HomeView.vue     # 首页
│   │   ├── LoginView.vue    # 登录页
│   │   ├── SignupView.vue   # 注册页
│   │   └── ChatView.vue     # 聊天页
│   ├── App.vue              # 根组件
│   └── main.js              # 入口文件
├── public/                  # 静态资源
├── package.json
└── README.md
```

## 页面功能

### 1. 首页 (HomeView)
- 复刻 ChatGPT 官网首页设计
- 响应式布局
- 功能介绍区块
- 导航到登录/注册页面

### 2. 登录页面 (LoginView)
- 邮箱登录表单
- 社交登录按钮 (Google, Microsoft, Apple)
- 表单验证
- 响应式设计

### 3. 注册页面 (SignupView)
- 用户注册表单
- 社交注册选项
- 表单验证
- 响应式设计

### 4. 聊天界面 (ChatView)
- 侧边栏聊天历史
- 主聊天区域
- 消息输入框
- 快捷操作按钮
- 响应式侧边栏

## 技术实现

### 状态管理
- **用户状态** (useUserStore): 管理用户登录状态、个人信息
- **聊天状态** (useChatStore): 管理聊天记录、当前对话
- **UI状态** (useUIStore): 管理界面状态、主题、通知

### 路由配置
- `/` - 首页
- `/login` - 登录页
- `/signup` - 注册页
- `/chat` - 聊天界面

### API 封装
- Axios 请求拦截器
- 响应拦截器
- 错误处理
- 统一的 API 接口管理

### 样式设计
- 复刻 ChatGPT 官网视觉设计
- 使用 Vuetify 组件库
- 自定义 CSS 样式
- 响应式布局
- 深色/浅色主题支持

## 安装和运行

### 安装依赖
```bash
npm install
```

### 开发模式运行
```bash
npm run serve
```

### 构建生产版本
```bash
npm run build
```

### 代码检查
```bash
npm run lint
```

## 主要依赖

- Vue 3
- Vuetify 3
- Vue Router 4
- Pinia
- Axios

## 浏览器支持

- Chrome (推荐)
- Firefox
- Safari
- Edge

## 开发说明

这是一个前端界面复刻项目，主要用于学习和展示目的。实际的 AI 对话功能需要连接到相应的后端 API 服务。

项目包含了完整的前端架构，包括状态管理、路由配置、API 封装等，可以作为 Vue 3 项目的参考模板。

## 许可证

MIT License

