<template>
  <v-app>
    <v-main>
      <v-container fluid class="login-container">
        <v-row justify="center" align="center" class="fill-height">
          <v-col cols="12" sm="8" md="6" lg="4" xl="3">
            <v-card class="login-card" elevation="0">
              <!-- OpenAI Logo -->
              <div class="text-center mb-8">
                <div class="openai-logo">
                  <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M22.2819 9.8211a5.9847 5.9847 0 0 0-.5157-4.9108 6.0462 6.0462 0 0 0-6.5098-2.9A6.0651 6.0651 0 0 0 4.9807 4.1818a5.9847 5.9847 0 0 0-3.9977 2.9 6.0462 6.0462 0 0 0 .7427 7.0966 5.98 5.98 0 0 0 .511 4.9107 6.051 6.051 0 0 0 6.5146 2.9001A5.9847 5.9847 0 0 0 13.2599 24a6.0557 6.0557 0 0 0 5.7718-4.2058 5.9894 5.9894 0 0 0 3.9977-2.9001 6.0557 6.0557 0 0 0-.7475-7.0729zm-9.022 12.6081a4.4755 4.4755 0 0 1-2.8764-1.0408l.1419-.0804 4.7783-2.7582a.7948.7948 0 0 0 .3927-.6813v-6.7369l2.02 1.1686a.071.071 0 0 1 .038.052v5.5826a4.504 4.504 0 0 1-4.4945 4.4944zm-9.6607-4.1254a4.4708 4.4708 0 0 1-.5346-3.0137l.142.0852 4.783 2.7582a.7712.7712 0 0 0 .7806 0l5.8428-3.3685v2.3324a.0804.0804 0 0 1-.0332.0615L9.74 19.9502a4.4992 4.4992 0 0 1-6.1408-1.6464zM2.3408 7.8956a4.485 4.485 0 0 1 2.3655-1.9728V11.6a.7664.7664 0 0 0 .3879.6765l5.8144 3.3543-2.0201 1.1685a.0757.0757 0 0 1-.071 0l-4.8303-2.7865A4.504 4.504 0 0 1 2.3408 7.872zm16.5963 3.8558L13.1038 8.364 15.1192 7.2a.0757.0757 0 0 1 .071 0l4.8303 2.7913a4.4944 4.4944 0 0 1-.6765 8.1042v-5.6772a.79.79 0 0 0-.407-.667zm2.0107-3.0231l-.142-.0852-4.7735-2.7818a.7759.7759 0 0 0-.7854 0L9.409 9.2297V6.8974a.0662.0662 0 0 1 .0284-.0615l4.8303-2.7866a4.4992 4.4992 0 0 1 6.6802 4.66zM8.3065 12.863l-2.02-1.1638a.0804.0804 0 0 1-.038-.0567V6.0742a4.4992 4.4992 0 0 1 7.3757-3.4537l-.142.0805L8.704 5.459a.7948.7948 0 0 0-.3927.6813zm1.0976-2.3654l2.602-1.4998 2.6069 1.4998v2.9994l-2.5974 1.4997-2.6067-1.4997Z" fill="#000"/>
                  </svg>
                </div>
              </div>

              <!-- Welcome Back Title -->
              <div class="text-center mb-6">
                <h1 class="welcome-title">Welcome back</h1>
              </div>

              <!-- Login Form -->
              <v-form @submit.prevent="handleLogin">
                <!-- Email Input -->
                <v-text-field
                  v-model="email"
                  label="Email address"
                  type="email"
                  variant="outlined"
                  class="mb-4"
                  :rules="emailRules"
                  required
                ></v-text-field>

                <!-- Continue Button -->
                <v-btn
                  type="submit"
                  color="#10a37f"
                  size="large"
                  block
                  class="continue-btn mb-4"
                  :loading="loading"
                >
                  Continue
                </v-btn>

                <!-- Sign up link -->
                <div class="text-center mb-6">
                  <span class="text-body-2">Don't have an account? </span>
                  <a href="#" class="sign-up-link" @click="goToSignUp">Sign up</a>
                </div>

                <!-- Divider -->
                <div class="divider-container mb-4">
                  <v-divider></v-divider>
                  <span class="divider-text">OR</span>
                  <v-divider></v-divider>
                </div>

                <!-- Social Login Buttons -->
                <v-btn
                  variant="outlined"
                  size="large"
                  block
                  class="social-btn mb-3"
                  @click="loginWithGoogle"
                >
                  <template v-slot:prepend>
                    <svg width="18" height="18" viewBox="0 0 24 24">
                      <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                      <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                      <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                      <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                    </svg>
                  </template>
                  Continue with Google
                </v-btn>

                <v-btn
                  variant="outlined"
                  size="large"
                  block
                  class="social-btn mb-3"
                  @click="loginWithMicrosoft"
                >
                  <template v-slot:prepend>
                    <svg width="18" height="18" viewBox="0 0 24 24">
                      <path fill="#f25022" d="M0 0h11.377v11.372H0z"/>
                      <path fill="#00a4ef" d="M12.623 0H24v11.372H12.623z"/>
                      <path fill="#7fba00" d="M0 12.628h11.377V24H0z"/>
                      <path fill="#ffb900" d="M12.623 12.628H24V24H12.623z"/>
                    </svg>
                  </template>
                  Continue with Microsoft Account
                </v-btn>

                <v-btn
                  variant="outlined"
                  size="large"
                  block
                  class="social-btn"
                  @click="loginWithApple"
                >
                  <template v-slot:prepend>
                    <svg width="18" height="18" viewBox="0 0 24 24">
                      <path fill="#000" d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"/>
                    </svg>
                  </template>
                  Continue with Apple
                </v-btn>
              </v-form>
            </v-card>
          </v-col>
        </v-row>
      </v-container>
    </v-main>
  </v-app>
</template>

<script>
import { useUserStore } from '@/stores/user'

export default {
  name: 'LoginView',
  data() {
    return {
      email: '',
      loading: false,
      emailRules: [
        v => !!v || 'Email is required',
        v => /.+@.+\..+/.test(v) || 'Email must be valid',
      ],
    }
  },
  setup() {
    const userStore = useUserStore()
    return { userStore }
  },
  methods: {
    async handleLogin() {
      this.loading = true
      try {
        // Simulate login API call
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        // Set user data in store
        this.userStore.setToken('fake-token')
        this.userStore.setName('User')
        
        // Redirect to chat
        this.$router.push('/chat')
      } catch (error) {
        console.error('Login failed:', error)
      } finally {
        this.loading = false
      }
    },
    goToSignUp() {
      // Handle sign up navigation
      console.log('Navigate to sign up')
    },
    loginWithGoogle() {
      console.log('Login with Google')
    },
    loginWithMicrosoft() {
      console.log('Login with Microsoft')
    },
    loginWithApple() {
      console.log('Login with Apple')
    }
  }
}
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 0;
}

.login-card {
  background: white;
  border-radius: 12px;
  padding: 48px 40px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
}

.openai-logo {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: #f7f7f8;
  border-radius: 8px;
}

.welcome-title {
  font-size: 32px;
  font-weight: 600;
  color: #2d3748;
  margin: 0;
}

.continue-btn {
  background-color: #10a37f !important;
  color: white !important;
  font-weight: 500;
  text-transform: none;
  border-radius: 6px;
  height: 52px;
}

.continue-btn:hover {
  background-color: #0d8f6b !important;
}

.sign-up-link {
  color: #10a37f;
  text-decoration: none;
  font-weight: 500;
}

.sign-up-link:hover {
  text-decoration: underline;
}

.divider-container {
  display: flex;
  align-items: center;
  gap: 16px;
}

.divider-text {
  color: #6b7280;
  font-size: 14px;
  white-space: nowrap;
}

.social-btn {
  border: 1px solid #d1d5db;
  color: #374151;
  font-weight: 500;
  text-transform: none;
  border-radius: 6px;
  height: 52px;
}

.social-btn:hover {
  background-color: #f9fafb;
}

.v-text-field {
  margin-bottom: 0;
}

:deep(.v-field__outline) {
  border-radius: 6px;
}

:deep(.v-field--focused .v-field__outline) {
  border-color: #10a37f;
}
</style>

