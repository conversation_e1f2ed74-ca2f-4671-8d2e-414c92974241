<template>
  <v-app>
    <!-- Navigation Drawer -->
    <v-navigation-drawer
      v-model="drawer"
      :rail="rail"
      permanent
      class="sidebar"
      width="260"
      rail-width="72"
    >
      <div class="sidebar-content">
        <!-- New Chat Button -->
        <v-btn
          class="new-chat-btn"
          variant="outlined"
          block
          @click="startNewChat"
          :class="{ 'rail-mode': rail }"
        >
          <v-icon v-if="rail" size="20">mdi-plus</v-icon>
          <template v-else>
            <v-icon start size="20">mdi-plus</v-icon>
            New chat
          </template>
        </v-btn>

        <!-- Chat History -->
        <div class="chat-history" v-if="!rail">
          <div class="history-section">
            <div class="history-label">Today</div>
            <div
              v-for="chat in todayChats"
              :key="chat.id"
              class="chat-item"
              :class="{ active: currentChatId === chat.id }"
              @click="selectChat(chat.id)"
            >
              <v-icon size="16" class="chat-icon">mdi-message-text</v-icon>
              <span class="chat-title">{{ chat.title }}</span>
              <v-btn
                icon
                size="x-small"
                variant="text"
                class="chat-menu-btn"
                @click.stop="showChatMenu(chat.id)"
              >
                <v-icon size="16">mdi-dots-horizontal</v-icon>
              </v-btn>
            </div>
          </div>

          <div class="history-section" v-if="yesterdayChats.length">
            <div class="history-label">Yesterday</div>
            <div
              v-for="chat in yesterdayChats"
              :key="chat.id"
              class="chat-item"
              :class="{ active: currentChatId === chat.id }"
              @click="selectChat(chat.id)"
            >
              <v-icon size="16" class="chat-icon">mdi-message-text</v-icon>
              <span class="chat-title">{{ chat.title }}</span>
              <v-btn
                icon
                size="x-small"
                variant="text"
                class="chat-menu-btn"
                @click.stop="showChatMenu(chat.id)"
              >
                <v-icon size="16">mdi-dots-horizontal</v-icon>
              </v-btn>
            </div>
          </div>
        </div>

        <!-- User Menu -->
        <div class="user-menu">
          <v-btn
            class="user-btn"
            variant="text"
            @click="showUserMenu"
            :class="{ 'rail-mode': rail }"
          >
            <v-avatar size="32" class="user-avatar">
              <v-img src="https://via.placeholder.com/32" alt="User"></v-img>
            </v-avatar>
            <span v-if="!rail" class="user-name">{{ userName }}</span>
            <v-icon v-if="!rail" size="16">mdi-dots-horizontal</v-icon>
          </v-btn>
        </div>
      </div>
    </v-navigation-drawer>

    <!-- Main Content -->
    <v-main class="main-content">
      <!-- Top Bar -->
      <v-app-bar flat class="top-bar" height="64">
        <v-btn
          icon
          variant="text"
          @click="rail = !rail"
          class="sidebar-toggle"
        >
          <v-icon>mdi-menu</v-icon>
        </v-btn>
        
        <v-spacer></v-spacer>
        
        <div class="model-selector">
          <v-btn
            variant="outlined"
            class="model-btn"
            @click="showModelMenu"
          >
            ChatGPT
            <v-icon end size="16">mdi-chevron-down</v-icon>
          </v-btn>
        </div>
      </v-app-bar>

      <!-- Chat Area -->
      <div class="chat-container">
        <!-- Welcome Screen -->
        <div v-if="!currentChatId" class="welcome-screen">
          <div class="welcome-content">
            <div class="chatgpt-logo">
              <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M22.2819 9.8211a5.9847 5.9847 0 0 0-.5157-4.9108 6.0462 6.0462 0 0 0-6.5098-2.9A6.0651 6.0651 0 0 0 4.9807 4.1818a5.9847 5.9847 0 0 0-3.9977 2.9 6.0462 6.0462 0 0 0 .7427 7.0966 5.98 5.98 0 0 0 .511 4.9107 6.051 6.051 0 0 0 6.5146 2.9001A5.9847 5.9847 0 0 0 13.2599 24a6.0557 6.0557 0 0 0 5.7718-4.2058 5.9894 5.9894 0 0 0 3.9977-2.9001 6.0557 6.0557 0 0 0-.7475-7.0729zm-9.022 12.6081a4.4755 4.4755 0 0 1-2.8764-1.0408l.1419-.0804 4.7783-2.7582a.7948.7948 0 0 0 .3927-.6813v-6.7369l2.02 1.1686a.071.071 0 0 1 .038.052v5.5826a4.504 4.504 0 0 1-4.4945 4.4944zm-9.6607-4.1254a4.4708 4.4708 0 0 1-.5346-3.0137l.142.0852 4.783 2.7582a.7712.7712 0 0 0 .7806 0l5.8428-3.3685v2.3324a.0804.0804 0 0 1-.0332.0615L9.74 19.9502a4.4992 4.4992 0 0 1-6.1408-1.6464zM2.3408 7.8956a4.485 4.485 0 0 1 2.3655-1.9728V11.6a.7664.7664 0 0 0 .3879.6765l5.8144 3.3543-2.0201 1.1685a.0757.0757 0 0 1-.071 0l-4.8303-2.7865A4.504 4.504 0 0 1 2.3408 7.872zm16.5963 3.8558L13.1038 8.364 15.1192 7.2a.0757.0757 0 0 1 .071 0l4.8303 2.7913a4.4944 4.4944 0 0 1-.6765 8.1042v-5.6772a.79.79 0 0 0-.407-.667zm2.0107-3.0231l-.142-.0852-4.7735-2.7818a.7759.7759 0 0 0-.7854 0L9.409 9.2297V6.8974a.0662.0662 0 0 1 .0284-.0615l4.8303-2.7866a4.4992 4.4992 0 0 1 6.6802 4.66zM8.3065 12.863l-2.02-1.1638a.0804.0804 0 0 1-.038-.0567V6.0742a4.4992 4.4992 0 0 1 7.3757-3.4537l-.142.0805L8.704 5.459a.7948.7948 0 0 0-.3927.6813zm1.0976-2.3654l2.602-1.4998 2.6069 1.4998v2.9994l-2.5974 1.4997-2.6067-1.4997Z" fill="#10a37f"/>
              </svg>
            </div>
            <h1 class="welcome-title">What can I help with?</h1>
            
            <!-- Quick Actions -->
            <div class="quick-actions">
              <div class="action-row">
                <v-btn
                  class="action-btn"
                  variant="outlined"
                  @click="useQuickAction('Attach')"
                >
                  <v-icon start>mdi-paperclip</v-icon>
                  Attach
                </v-btn>
                <v-btn
                  class="action-btn"
                  variant="outlined"
                  @click="useQuickAction('Search')"
                >
                  <v-icon start>mdi-magnify</v-icon>
                  Search
                </v-btn>
                <v-btn
                  class="action-btn"
                  variant="outlined"
                  @click="useQuickAction('Reason')"
                >
                  <v-icon start>mdi-brain</v-icon>
                  Reason
                </v-btn>
                <v-btn
                  class="action-btn"
                  variant="outlined"
                  @click="useQuickAction('Voice')"
                >
                  <v-icon start>mdi-microphone</v-icon>
                  Voice
                </v-btn>
              </div>
            </div>
          </div>
        </div>

        <!-- Chat Messages -->
        <div v-else class="chat-messages">
          <div
            v-for="message in currentChat?.messages || []"
            :key="message.id"
            class="message"
            :class="{ 'user-message': message.role === 'user', 'assistant-message': message.role === 'assistant' }"
          >
            <div class="message-avatar">
              <v-avatar size="32">
                <v-img
                  v-if="message.role === 'user'"
                  src="https://via.placeholder.com/32"
                  alt="User"
                ></v-img>
                <div v-else class="assistant-avatar">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M22.2819 9.8211a5.9847 5.9847 0 0 0-.5157-4.9108 6.0462 6.0462 0 0 0-6.5098-2.9A6.0651 6.0651 0 0 0 4.9807 4.1818a5.9847 5.9847 0 0 0-3.9977 2.9 6.0462 6.0462 0 0 0 .7427 7.0966 5.98 5.98 0 0 0 .511 4.9107 6.051 6.051 0 0 0 6.5146 2.9001A5.9847 5.9847 0 0 0 13.2599 24a6.0557 6.0557 0 0 0 5.7718-4.2058 5.9894 5.9894 0 0 0 3.9977-2.9001 6.0557 6.0557 0 0 0-.7475-7.0729zm-9.022 12.6081a4.4755 4.4755 0 0 1-2.8764-1.0408l.1419-.0804 4.7783-2.7582a.7948.7948 0 0 0 .3927-.6813v-6.7369l2.02 1.1686a.071.071 0 0 1 .038.052v5.5826a4.504 4.504 0 0 1-4.4945 4.4944zm-9.6607-4.1254a4.4708 4.4708 0 0 1-.5346-3.0137l.142.0852 4.783 2.7582a.7712.7712 0 0 0 .7806 0l5.8428-3.3685v2.3324a.0804.0804 0 0 1-.0332.0615L9.74 19.9502a4.4992 4.4992 0 0 1-6.1408-1.6464zM2.3408 7.8956a4.485 4.485 0 0 1 2.3655-1.9728V11.6a.7664.7664 0 0 0 .3879.6765l5.8144 3.3543-2.0201 1.1685a.0757.0757 0 0 1-.071 0l-4.8303-2.7865A4.504 4.504 0 0 1 2.3408 7.872zm16.5963 3.8558L13.1038 8.364 15.1192 7.2a.0757.0757 0 0 1 .071 0l4.8303 2.7913a4.4944 4.4944 0 0 1-.6765 8.1042v-5.6772a.79.79 0 0 0-.407-.667zm2.0107-3.0231l-.142-.0852-4.7735-2.7818a.7759.7759 0 0 0-.7854 0L9.409 9.2297V6.8974a.0662.0662 0 0 1 .0284-.0615l4.8303-2.7866a4.4992 4.4992 0 0 1 6.6802 4.66zM8.3065 12.863l-2.02-1.1638a.0804.0804 0 0 1-.038-.0567V6.0742a4.4992 4.4992 0 0 1 7.3757-3.4537l-.142.0805L8.704 5.459a.7948.7948 0 0 0-.3927.6813zm1.0976-2.3654l2.602-1.4998 2.6069 1.4998v2.9994l-2.5974 1.4997-2.6067-1.4997Z" fill="#10a37f"/>
                  </svg>
                </div>
              </v-avatar>
            </div>
            <div class="message-content">
              <div class="message-text">{{ message.content }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Input Area -->
      <div class="input-area">
        <div class="input-container">
          <v-text-field
            v-model="inputMessage"
            placeholder="Ask anything"
            variant="outlined"
            hide-details
            class="message-input"
            @keydown.enter="sendMessage"
          >
            <template v-slot:prepend-inner>
              <v-btn
                icon
                size="small"
                variant="text"
                @click="attachFile"
              >
                <v-icon size="20">mdi-paperclip</v-icon>
              </v-btn>
            </template>
            <template v-slot:append-inner>
              <v-btn
                icon
                size="small"
                variant="text"
                :disabled="!inputMessage.trim()"
                @click="sendMessage"
                class="send-btn"
              >
                <v-icon size="20">mdi-send</v-icon>
              </v-btn>
            </template>
          </v-text-field>
        </div>
        <div class="input-footer">
          <p class="disclaimer">
            By messaging ChatGPT, you agree to our 
            <a href="#" class="link">Terms</a> and have read our 
            <a href="#" class="link">Privacy Policy</a>.
          </p>
        </div>
      </div>
    </v-main>
  </v-app>
</template>

<script>
import { useUserStore } from '@/stores/user'

export default {
  name: 'ChatView',
  data() {
    return {
      drawer: true,
      rail: false,
      inputMessage: '',
      currentChatId: null,
      chats: [
        {
          id: 1,
          title: 'Vue.js best practices',
          date: new Date(),
          messages: [
            { id: 1, role: 'user', content: 'What are some Vue.js best practices?' },
            { id: 2, role: 'assistant', content: 'Here are some Vue.js best practices:\n\n1. Use composition API for complex components\n2. Keep components small and focused\n3. Use proper naming conventions\n4. Implement proper error handling\n5. Use TypeScript for better type safety' }
          ]
        },
        {
          id: 2,
          title: 'JavaScript async/await',
          date: new Date(Date.now() - 86400000), // Yesterday
          messages: [
            { id: 1, role: 'user', content: 'Explain async/await in JavaScript' },
            { id: 2, role: 'assistant', content: 'Async/await is a syntax that makes it easier to work with promises in JavaScript...' }
          ]
        }
      ]
    }
  },
  setup() {
    const userStore = useUserStore()
    return { userStore }
  },
  computed: {
    userName() {
      return this.userStore.name || 'User'
    },
    todayChats() {
      const today = new Date().toDateString()
      return this.chats.filter(chat => chat.date.toDateString() === today)
    },
    yesterdayChats() {
      const yesterday = new Date(Date.now() - 86400000).toDateString()
      return this.chats.filter(chat => chat.date.toDateString() === yesterday)
    },
    currentChat() {
      return this.chats.find(chat => chat.id === this.currentChatId)
    }
  },
  methods: {
    startNewChat() {
      this.currentChatId = null
      this.inputMessage = ''
    },
    selectChat(chatId) {
      this.currentChatId = chatId
    },
    showChatMenu(chatId) {
      console.log('Show chat menu for:', chatId)
    },
    showUserMenu() {
      console.log('Show user menu')
    },
    showModelMenu() {
      console.log('Show model menu')
    },
    useQuickAction(action) {
      console.log('Quick action:', action)
    },
    attachFile() {
      console.log('Attach file')
    },
    sendMessage() {
      if (!this.inputMessage.trim()) return

      // If no current chat, create a new one
      if (!this.currentChatId) {
        const newChat = {
          id: Date.now(),
          title: this.inputMessage.slice(0, 30) + (this.inputMessage.length > 30 ? '...' : ''),
          date: new Date(),
          messages: []
        }
        this.chats.unshift(newChat)
        this.currentChatId = newChat.id
      }

      // Add user message
      const userMessage = {
        id: Date.now(),
        role: 'user',
        content: this.inputMessage
      }
      
      const chat = this.chats.find(c => c.id === this.currentChatId)
      if (chat) {
        chat.messages.push(userMessage)
        
        // Simulate assistant response
        setTimeout(() => {
          const assistantMessage = {
            id: Date.now() + 1,
            role: 'assistant',
            content: 'This is a simulated response from ChatGPT. In a real implementation, this would be connected to an AI API.'
          }
          chat.messages.push(assistantMessage)
        }, 1000)
      }

      this.inputMessage = ''
    }
  }
}
</script>

<style scoped>
.sidebar {
  background: #171717 !important;
  border-right: 1px solid #2d2d2d;
}

.sidebar-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 12px;
}

.new-chat-btn {
  border: 1px solid #4d4d4d;
  color: #ececec;
  margin-bottom: 16px;
  height: 44px;
  text-transform: none;
  font-weight: 500;
}

.new-chat-btn.rail-mode {
  width: 44px;
  min-width: 44px;
}

.new-chat-btn:hover {
  background-color: #2d2d2d;
}

.chat-history {
  flex: 1;
  overflow-y: auto;
}

.history-section {
  margin-bottom: 16px;
}

.history-label {
  color: #8e8ea0;
  font-size: 12px;
  font-weight: 600;
  margin-bottom: 8px;
  padding: 0 8px;
}

.chat-item {
  display: flex;
  align-items: center;
  padding: 8px;
  border-radius: 8px;
  cursor: pointer;
  color: #ececec;
  margin-bottom: 2px;
  transition: background-color 0.2s;
}

.chat-item:hover {
  background-color: #2d2d2d;
}

.chat-item.active {
  background-color: #2d2d2d;
}

.chat-icon {
  margin-right: 8px;
  color: #8e8ea0;
}

.chat-title {
  flex: 1;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.chat-menu-btn {
  opacity: 0;
  transition: opacity 0.2s;
}

.chat-item:hover .chat-menu-btn {
  opacity: 1;
}

.user-menu {
  border-top: 1px solid #2d2d2d;
  padding-top: 12px;
}

.user-btn {
  width: 100%;
  justify-content: flex-start;
  color: #ececec;
  text-transform: none;
  height: 44px;
  padding: 0 8px;
}

.user-btn.rail-mode {
  justify-content: center;
  padding: 0;
}

.user-avatar {
  margin-right: 8px;
}

.user-btn.rail-mode .user-avatar {
  margin-right: 0;
}

.user-name {
  flex: 1;
  text-align: left;
  font-weight: 500;
}

.top-bar {
  background: white !important;
  border-bottom: 1px solid #e5e5e5;
}

.sidebar-toggle {
  margin-left: 8px;
}

.model-selector {
  margin-right: 16px;
}

.model-btn {
  border: 1px solid #d1d5db;
  color: #374151;
  text-transform: none;
  font-weight: 500;
}

.main-content {
  background: #f7f7f8;
}

.chat-container {
  height: calc(100vh - 64px - 120px);
  overflow-y: auto;
  padding: 0 16px;
}

.welcome-screen {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
}

.welcome-content {
  max-width: 600px;
  width: 100%;
}

.chatgpt-logo {
  margin-bottom: 24px;
}

.welcome-title {
  font-size: 32px;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 32px;
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.action-row {
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
}

.action-btn {
  border: 1px solid #d1d5db;
  color: #374151;
  text-transform: none;
  font-weight: 500;
  min-width: 120px;
}

.chat-messages {
  padding: 24px 0;
}

.message {
  display: flex;
  margin-bottom: 24px;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.message-avatar {
  margin-right: 16px;
  flex-shrink: 0;
}

.assistant-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: #10a37f;
  border-radius: 50%;
}

.message-content {
  flex: 1;
}

.message-text {
  font-size: 16px;
  line-height: 1.6;
  color: #2d3748;
  white-space: pre-wrap;
}

.input-area {
  position: fixed;
  bottom: 0;
  left: 260px;
  right: 0;
  background: #f7f7f8;
  padding: 16px;
  border-top: 1px solid #e5e5e5;
  transition: left 0.3s;
}

.rail .input-area {
  left: 72px;
}

.input-container {
  max-width: 800px;
  margin: 0 auto;
}

.message-input {
  background: white;
  border-radius: 24px;
}

.message-input :deep(.v-field__outline) {
  border-radius: 24px;
  border-color: #d1d5db;
}

.message-input :deep(.v-field--focused .v-field__outline) {
  border-color: #10a37f;
}

.send-btn {
  color: #10a37f;
}

.send-btn:disabled {
  color: #9ca3af;
}

.input-footer {
  text-align: center;
  margin-top: 8px;
}

.disclaimer {
  font-size: 12px;
  color: #6b7280;
  margin: 0;
}

.link {
  color: #10a37f;
  text-decoration: none;
}

.link:hover {
  text-decoration: underline;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .input-area {
    left: 0;
    right: 0;
  }
  
  .action-row {
    flex-direction: column;
    align-items: center;
  }
  
  .action-btn {
    width: 200px;
  }
}
</style>

