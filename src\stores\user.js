import { defineStore } from 'pinia'

export const useUserStore = defineStore('user', {
  state: () => ({
    token: null,
    name: '',
    avatar: ''
  }),
  actions: {
    setToken(token) {
      this.token = token
    },
    setName(name) {
      this.name = name
    },
    setAvatar(avatar) {
      this.avatar = avatar
    },
    resetUser() {
      this.token = null
      this.name = ''
      this.avatar = ''
    }
  }
})


