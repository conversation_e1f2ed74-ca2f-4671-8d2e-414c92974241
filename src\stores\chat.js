import { defineStore } from 'pinia'

export const useChatStore = defineStore('chat', {
  state: () => ({
    chats: [],
    currentChatId: null,
    isLoading: false,
    error: null
  }),
  
  getters: {
    currentChat: (state) => {
      return state.chats.find(chat => chat.id === state.currentChatId)
    },
    
    todayChats: (state) => {
      const today = new Date().toDateString()
      return state.chats.filter(chat => chat.date.toDateString() === today)
    },
    
    yesterdayChats: (state) => {
      const yesterday = new Date(Date.now() - 86400000).toDateString()
      return state.chats.filter(chat => chat.date.toDateString() === yesterday)
    },
    
    olderChats: (state) => {
      const yesterday = new Date(Date.now() - 86400000).toDateString()
      const today = new Date().toDateString()
      return state.chats.filter(chat => {
        const chatDate = chat.date.toDateString()
        return chatDate !== today && chatDate !== yesterday
      })
    }
  },
  
  actions: {
    createNewChat() {
      const newChat = {
        id: Date.now(),
        title: 'New Chat',
        date: new Date(),
        messages: []
      }
      this.chats.unshift(newChat)
      this.currentChatId = newChat.id
      return newChat
    },
    
    selectChat(chatId) {
      this.currentChatId = chatId
    },
    
    addMessage(message) {
      const chat = this.currentChat
      if (chat) {
        chat.messages.push({
          id: Date.now(),
          ...message,
          timestamp: new Date()
        })
        
        // Update chat title if it's the first user message
        if (chat.messages.length === 1 && message.role === 'user') {
          chat.title = message.content.slice(0, 30) + (message.content.length > 30 ? '...' : '')
        }
      }
    },
    
    deleteChat(chatId) {
      const index = this.chats.findIndex(chat => chat.id === chatId)
      if (index !== -1) {
        this.chats.splice(index, 1)
        if (this.currentChatId === chatId) {
          this.currentChatId = this.chats.length > 0 ? this.chats[0].id : null
        }
      }
    },
    
    clearAllChats() {
      this.chats = []
      this.currentChatId = null
    },
    
    setLoading(loading) {
      this.isLoading = loading
    },
    
    setError(error) {
      this.error = error
    }
  }
})

