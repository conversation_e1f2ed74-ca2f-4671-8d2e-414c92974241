// API endpoints and methods for the ChatGPT clone

import request from '@/utils/request'

// Auth API
export const authAPI = {
  // Login with email and password
  login(credentials) {
    return request({
      url: '/auth/login',
      method: 'post',
      data: credentials
    })
  },

  // Register new user
  register(userData) {
    return request({
      url: '/auth/register',
      method: 'post',
      data: userData
    })
  },

  // Logout user
  logout() {
    return request({
      url: '/auth/logout',
      method: 'post'
    })
  },

  // Refresh access token
  refreshToken(refreshToken) {
    return request({
      url: '/auth/refresh',
      method: 'post',
      data: { refreshToken }
    })
  },

  // Get current user info
  getCurrentUser() {
    return request({
      url: '/auth/me',
      method: 'get'
    })
  },

  // Social login
  socialLogin(provider, token) {
    return request({
      url: `/auth/social/${provider}`,
      method: 'post',
      data: { token }
    })
  },

  // Reset password
  resetPassword(email) {
    return request({
      url: '/auth/reset-password',
      method: 'post',
      data: { email }
    })
  },

  // Confirm password reset
  confirmPasswordReset(token, newPassword) {
    return request({
      url: '/auth/confirm-reset',
      method: 'post',
      data: { token, newPassword }
    })
  }
}

// Chat API
export const chatAPI = {
  // Get user's chat history
  getChats() {
    return request({
      url: '/chats',
      method: 'get'
    })
  },

  // Create new chat
  createChat(title = 'New Chat') {
    return request({
      url: '/chats',
      method: 'post',
      data: { title }
    })
  },

  // Get specific chat with messages
  getChat(chatId) {
    return request({
      url: `/chats/${chatId}`,
      method: 'get'
    })
  },

  // Update chat title
  updateChat(chatId, data) {
    return request({
      url: `/chats/${chatId}`,
      method: 'patch',
      data
    })
  },

  // Delete chat
  deleteChat(chatId) {
    return request({
      url: `/chats/${chatId}`,
      method: 'delete'
    })
  },

  // Send message to chat
  sendMessage(chatId, message) {
    return request({
      url: `/chats/${chatId}/messages`,
      method: 'post',
      data: { message }
    })
  },

  // Get chat messages
  getMessages(chatId, page = 1, limit = 50) {
    return request({
      url: `/chats/${chatId}/messages`,
      method: 'get',
      params: { page, limit }
    })
  },

  // Stream chat response (for real-time responses)
  streamMessage(chatId, message) {
    return request({
      url: `/chats/${chatId}/stream`,
      method: 'post',
      data: { message },
      responseType: 'stream'
    })
  },

  // Regenerate response
  regenerateResponse(chatId, messageId) {
    return request({
      url: `/chats/${chatId}/messages/${messageId}/regenerate`,
      method: 'post'
    })
  },

  // Rate message
  rateMessage(chatId, messageId, rating) {
    return request({
      url: `/chats/${chatId}/messages/${messageId}/rate`,
      method: 'post',
      data: { rating }
    })
  }
}

// User API
export const userAPI = {
  // Get user profile
  getProfile() {
    return request({
      url: '/user/profile',
      method: 'get'
    })
  },

  // Update user profile
  updateProfile(data) {
    return request({
      url: '/user/profile',
      method: 'patch',
      data
    })
  },

  // Upload avatar
  uploadAvatar(file) {
    const formData = new FormData()
    formData.append('avatar', file)
    
    return request({
      url: '/user/avatar',
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // Get user settings
  getSettings() {
    return request({
      url: '/user/settings',
      method: 'get'
    })
  },

  // Update user settings
  updateSettings(settings) {
    return request({
      url: '/user/settings',
      method: 'patch',
      data: settings
    })
  },

  // Delete user account
  deleteAccount() {
    return request({
      url: '/user/account',
      method: 'delete'
    })
  }
}

// File API
export const fileAPI = {
  // Upload file
  uploadFile(file, chatId = null) {
    const formData = new FormData()
    formData.append('file', file)
    if (chatId) {
      formData.append('chatId', chatId)
    }
    
    return request({
      url: '/files/upload',
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // Get file info
  getFile(fileId) {
    return request({
      url: `/files/${fileId}`,
      method: 'get'
    })
  },

  // Download file
  downloadFile(fileId) {
    return request({
      url: `/files/${fileId}/download`,
      method: 'get',
      responseType: 'blob'
    })
  },

  // Delete file
  deleteFile(fileId) {
    return request({
      url: `/files/${fileId}`,
      method: 'delete'
    })
  },

  // Get user's files
  getUserFiles(page = 1, limit = 20) {
    return request({
      url: '/files',
      method: 'get',
      params: { page, limit }
    })
  }
}

// Models API
export const modelsAPI = {
  // Get available models
  getModels() {
    return request({
      url: '/models',
      method: 'get'
    })
  },

  // Get model details
  getModel(modelId) {
    return request({
      url: `/models/${modelId}`,
      method: 'get'
    })
  },

  // Get user's model preferences
  getModelPreferences() {
    return request({
      url: '/models/preferences',
      method: 'get'
    })
  },

  // Update model preferences
  updateModelPreferences(preferences) {
    return request({
      url: '/models/preferences',
      method: 'patch',
      data: preferences
    })
  }
}

// Search API
export const searchAPI = {
  // Search chats
  searchChats(query, filters = {}) {
    return request({
      url: '/search/chats',
      method: 'get',
      params: { query, ...filters }
    })
  },

  // Search messages
  searchMessages(query, chatId = null, filters = {}) {
    return request({
      url: '/search/messages',
      method: 'get',
      params: { query, chatId, ...filters }
    })
  },

  // Get search suggestions
  getSearchSuggestions(query) {
    return request({
      url: '/search/suggestions',
      method: 'get',
      params: { query }
    })
  }
}

// Analytics API (if needed)
export const analyticsAPI = {
  // Track user event
  trackEvent(event, properties = {}) {
    return request({
      url: '/analytics/events',
      method: 'post',
      data: { event, properties }
    })
  },

  // Get user analytics
  getUserAnalytics(timeRange = '30d') {
    return request({
      url: '/analytics/user',
      method: 'get',
      params: { timeRange }
    })
  }
}

// Export all APIs
export default {
  auth: authAPI,
  chat: chatAPI,
  user: userAPI,
  file: fileAPI,
  models: modelsAPI,
  search: searchAPI,
  analytics: analyticsAPI
}

